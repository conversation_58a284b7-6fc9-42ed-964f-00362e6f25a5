<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Include shared settings functions
require_once 'includes/settings_functions.php';

// Include header first to load language system
include 'includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $allSettings = [];

        // Organization & Branding Settings
        if (isset($_POST['organization'])) {
            $allSettings = array_merge($allSettings, [
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'organization_name' => $_POST['organization_name'] ?? '',
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'footer_text' => $_POST['footer_text'] ?? ''
            ]);
        }

        // Contact Information
        if (isset($_POST['contact'])) {
            $allSettings = array_merge($allSettings, [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'office_hours' => $_POST['office_hours'] ?? '',
                'emergency_contact' => $_POST['emergency_contact'] ?? ''
            ]);
        }

        // Social Media
        if (isset($_POST['social'])) {
            $allSettings = array_merge($allSettings, [
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'linkedin_url' => $_POST['linkedin_url'] ?? '',
                'tiktok_url' => $_POST['tiktok_url'] ?? '',
                'website_url' => $_POST['website_url'] ?? '',
                'blog_url' => $_POST['blog_url'] ?? ''
            ]);
        }

        // Email Configuration
        if (isset($_POST['email'])) {
            $allSettings = array_merge($allSettings, [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'from_email' => $_POST['from_email'] ?? '',
                'from_name' => $_POST['from_name'] ?? '',
                'reply_to_email' => $_POST['reply_to_email'] ?? '',
                'email_signature' => $_POST['email_signature'] ?? '',
                'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
            ]);
        }

        // System Preferences
        if (isset($_POST['system'])) {
            $allSettings = array_merge($allSettings, [
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                'currency_code' => $_POST['currency_code'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'items_per_page' => $_POST['items_per_page'] ?? '25',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
            ]);
        }

        // Save all settings (updateSettings handles its own transaction)
        if (!empty($allSettings) && updateSettings($allSettings)) {
            // If email settings were updated, sync them to email_settings table
            if (isset($_POST['email'])) {
                try {
                    // Map settings table keys to email_settings table keys
                    $emailSettingsData = [];
                    $emailKeys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name'];

                    foreach ($emailKeys as $key) {
                        if (isset($allSettings[$key])) {
                            switch ($key) {
                                case 'smtp_encryption':
                                    $emailSettingsData['smtp_secure'] = $allSettings[$key];
                                    break;
                                case 'from_email':
                                    $emailSettingsData['sender_email'] = $allSettings[$key];
                                    break;
                                case 'from_name':
                                    $emailSettingsData['sender_name'] = $allSettings[$key];
                                    break;
                                default:
                                    $emailSettingsData[$key] = $allSettings[$key];
                                    break;
                            }
                        }
                    }

                    // Add default smtp_auth
                    $emailSettingsData['smtp_auth'] = '1';

                    // Update email_settings table
                    $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");

                    foreach ($emailSettingsData as $key => $value) {
                        $stmt->execute([$key, $value]);
                    }

                    error_log("Email settings synchronized to email_settings table");
                } catch (Exception $e) {
                    error_log("Failed to sync email settings: " . $e->getMessage());
                    // Don't fail the main operation, just log the error
                }
            }

            $success_message = __('settings_updated_successfully');
        } else {
            throw new Exception('Failed to update settings or no settings provided');
        }

    } catch (Exception $e) {
        $error_message = __('error') . ": " . $e->getMessage();
    }
}

// Get current settings
$currentSettings = getCurrentSettings();

// Page title and header info (after language system is loaded)
$page_title = __('system_settings');
$page_header = __('system_settings');
$page_description = __('manage_system_settings_description');


?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="bi bi-gear-fill"></i> <?php _e('system_settings'); ?>
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="logo_management_consolidated.php" class="btn btn-outline-secondary">
                    <i class="bi bi-image"></i> <?php _e('logo_management'); ?>
                </a>
                <a href="security_settings.php" class="btn btn-outline-secondary">
                    <i class="bi bi-shield-lock"></i> <?php _e('security_settings'); ?>
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                <i class="bi bi-building"></i> <?php _e('organization_branding'); ?>
            </button>
        </li>

        <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                <i class="bi bi-telephone"></i> <?php _e('contact_info'); ?>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                <i class="bi bi-share"></i> <?php _e('social_media'); ?>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                <i class="bi bi-envelope"></i> <?php _e('email_config'); ?>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                <i class="bi bi-cpu"></i> <?php _e('system_preferences'); ?>
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="settingsTabContent">

        <!-- Organization & Branding Tab -->
        <div class="tab-pane fade show active" id="organization" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="organization" value="1">

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-building"></i> <?php _e('organization_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="site_title" class="form-label"><?php _e('site_title'); ?></label>
                                <input type="text" class="form-control" id="site_title" name="site_title"
                                       value="<?php echo htmlspecialchars($currentSettings['site_title']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="admin_title" class="form-label"><?php _e('admin_title'); ?></label>
                                <input type="text" class="form-control" id="admin_title" name="admin_title"
                                       value="<?php echo htmlspecialchars($currentSettings['admin_title']); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="organization_name" class="form-label"><?php _e('organization_name'); ?></label>
                                <input type="text" class="form-control" id="organization_name" name="organization_name"
                                       value="<?php echo htmlspecialchars($currentSettings['organization_name']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="organization_type" class="form-label"><?php _e('organization_type'); ?></label>
                                <select class="form-select" id="organization_type" name="organization_type">
                                    <option value="church" <?php echo $currentSettings['organization_type'] === 'church' ? 'selected' : ''; ?>><?php _e('church'); ?></option>
                                    <option value="nonprofit" <?php echo $currentSettings['organization_type'] === 'nonprofit' ? 'selected' : ''; ?>><?php _e('nonprofit'); ?></option>
                                    <option value="ministry" <?php echo $currentSettings['organization_type'] === 'ministry' ? 'selected' : ''; ?>><?php _e('ministry'); ?></option>
                                    <option value="community" <?php echo $currentSettings['organization_type'] === 'community' ? 'selected' : ''; ?>><?php _e('community_group'); ?></option>
                                    <option value="other" <?php echo $currentSettings['organization_type'] === 'other' ? 'selected' : ''; ?>><?php _e('other'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="organization_mission" class="form-label"><?php _e('mission_statement'); ?></label>
                            <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($currentSettings['organization_mission']); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="organization_vision" class="form-label"><?php _e('vision_statement'); ?></label>
                            <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($currentSettings['organization_vision']); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="organization_values" class="form-label"><?php _e('core_values'); ?></label>
                            <textarea class="form-control" id="organization_values" name="organization_values" rows="3"><?php echo htmlspecialchars($currentSettings['organization_values']); ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-tags"></i> <?php _e('terminology'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="member_term" class="form-label"><?php _e('member_term'); ?></label>
                                <input type="text" class="form-control" id="member_term" name="member_term"
                                       value="<?php echo htmlspecialchars($currentSettings['member_term']); ?>" placeholder="Member">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="leader_term" class="form-label"><?php _e('leader_term'); ?></label>
                                <input type="text" class="form-control" id="leader_term" name="leader_term"
                                       value="<?php echo htmlspecialchars($currentSettings['leader_term']); ?>" placeholder="Pastor">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="group_term" class="form-label"><?php _e('group_term'); ?></label>
                                <input type="text" class="form-control" id="group_term" name="group_term"
                                       value="<?php echo htmlspecialchars($currentSettings['group_term']); ?>" placeholder="Ministry">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_term" class="form-label"><?php _e('event_term'); ?></label>
                                <input type="text" class="form-control" id="event_term" name="event_term"
                                       value="<?php echo htmlspecialchars($currentSettings['event_term']); ?>" placeholder="Service">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="donation_term" class="form-label"><?php _e('donation_term'); ?></label>
                                <input type="text" class="form-control" id="donation_term" name="donation_term"
                                       value="<?php echo htmlspecialchars($currentSettings['donation_term']); ?>" placeholder="Offering">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-text"></i> <?php _e('footer_content'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="footer_text" class="form-label"><?php _e('footer_text'); ?></label>
                            <textarea class="form-control" id="footer_text" name="footer_text" rows="2"><?php echo htmlspecialchars($currentSettings['footer_text']); ?></textarea>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> <?php _e('save_organization_settings'); ?>
                </button>
            </form>
        </div>

        <!-- Contact Information Tab -->
        <div class="tab-pane fade" id="contact" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="contact" value="1">

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-telephone"></i> <?php _e('contact_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label"><?php _e('phone_number'); ?></label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label"><?php _e('email_address'); ?></label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="contact_address" class="form-label"><?php _e('street_address'); ?></label>
                            <input type="text" class="form-control" id="contact_address" name="contact_address"
                                   value="<?php echo htmlspecialchars($currentSettings['contact_address']); ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="contact_city" class="form-label"><?php _e('city'); ?></label>
                                <input type="text" class="form-control" id="contact_city" name="contact_city"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_city']); ?>">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="contact_state" class="form-label"><?php _e('state_province'); ?></label>
                                <input type="text" class="form-control" id="contact_state" name="contact_state"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_state']); ?>">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="contact_zip" class="form-label"><?php _e('zip_postal_code'); ?></label>
                                <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_zip']); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_country" class="form-label"><?php _e('country'); ?></label>
                                <input type="text" class="form-control" id="contact_country" name="contact_country"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_country']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="emergency_contact" class="form-label"><?php _e('emergency_contact'); ?></label>
                                <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                       value="<?php echo htmlspecialchars($currentSettings['emergency_contact']); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="office_hours" class="form-label"><?php _e('office_hours'); ?></label>
                            <textarea class="form-control" id="office_hours" name="office_hours" rows="3"><?php echo htmlspecialchars($currentSettings['office_hours']); ?></textarea>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> <?php _e('save_contact_settings'); ?>
                </button>
            </form>
        </div>

        <!-- Social Media Tab -->
        <div class="tab-pane fade" id="social" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="social" value="1">

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-share"></i> <?php _e('social_media_links'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="facebook_url" class="form-label">
                                    <i class="bi bi-facebook"></i> <?php _e('facebook_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                       value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>" placeholder="https://facebook.com/yourpage">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="twitter_url" class="form-label">
                                    <i class="bi bi-twitter"></i> <?php _e('twitter_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                       value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>" placeholder="https://twitter.com/yourhandle">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="instagram_url" class="form-label">
                                    <i class="bi bi-instagram"></i> <?php _e('instagram_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                       value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>" placeholder="https://instagram.com/yourhandle">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="youtube_url" class="form-label">
                                    <i class="bi bi-youtube"></i> <?php _e('youtube_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                       value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>" placeholder="https://youtube.com/yourchannel">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="linkedin_url" class="form-label">
                                    <i class="bi bi-linkedin"></i> <?php _e('linkedin_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                       value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>" placeholder="https://linkedin.com/company/yourcompany">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="website_url" class="form-label">
                                    <i class="bi bi-globe"></i> <?php _e('website_url'); ?>
                                </label>
                                <input type="url" class="form-control" id="website_url" name="website_url"
                                       value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>" placeholder="https://yourwebsite.com">
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> <?php _e('save_social_settings'); ?>
                </button>
            </form>
        </div>

        <!-- Email Configuration Tab -->
        <div class="tab-pane fade" id="email" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="email" value="1">

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-envelope-gear"></i> <?php _e('smtp_configuration'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_host" class="form-label"><?php _e('smtp_host'); ?></label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>" placeholder="smtp.gmail.com">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="smtp_port" class="form-label"><?php _e('smtp_port'); ?></label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_port']); ?>" placeholder="587">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_username" class="form-label"><?php _e('smtp_username'); ?></label>
                                <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="smtp_password" class="form-label"><?php _e('smtp_password'); ?></label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_encryption" class="form-label"><?php _e('encryption'); ?></label>
                                <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                    <option value="tls" <?php echo ($currentSettings['smtp_encryption'] === 'tls') ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo ($currentSettings['smtp_encryption'] === 'ssl') ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo ($currentSettings['smtp_encryption'] === 'none') ? 'selected' : ''; ?>><?php _e('none'); ?></option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                           <?php echo ($currentSettings['enable_email_queue'] === '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_email_queue">
                                        <?php _e('enable_email_queue'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-envelope"></i> <?php _e('email_defaults'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="from_email" class="form-label"><?php _e('from_email'); ?></label>
                                <input type="email" class="form-control" id="from_email" name="from_email"
                                       value="<?php echo htmlspecialchars($currentSettings['from_email']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="from_name" class="form-label"><?php _e('from_name'); ?></label>
                                <input type="text" class="form-control" id="from_name" name="from_name"
                                       value="<?php echo htmlspecialchars($currentSettings['from_name']); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reply_to_email" class="form-label"><?php _e('reply_to_email'); ?></label>
                            <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                   value="<?php echo htmlspecialchars($currentSettings['reply_to_email']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="email_signature" class="form-label"><?php _e('email_signature'); ?></label>
                            <textarea class="form-control" id="email_signature" name="email_signature" rows="4"><?php echo htmlspecialchars($currentSettings['email_signature']); ?></textarea>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> <?php _e('save_email_settings'); ?>
                </button>
            </form>
        </div>

        <!-- System Preferences Tab -->
        <div class="tab-pane fade" id="system" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="system" value="1">

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock"></i> <?php _e('regional_settings'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="timezone" class="form-label"><?php _e('timezone'); ?></label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="America/New_York" <?php echo ($currentSettings['timezone'] === 'America/New_York') ? 'selected' : ''; ?>>Eastern Time</option>
                                    <option value="America/Chicago" <?php echo ($currentSettings['timezone'] === 'America/Chicago') ? 'selected' : ''; ?>>Central Time</option>
                                    <option value="America/Denver" <?php echo ($currentSettings['timezone'] === 'America/Denver') ? 'selected' : ''; ?>>Mountain Time</option>
                                    <option value="America/Los_Angeles" <?php echo ($currentSettings['timezone'] === 'America/Los_Angeles') ? 'selected' : ''; ?>>Pacific Time</option>
                                    <option value="UTC" <?php echo ($currentSettings['timezone'] === 'UTC') ? 'selected' : ''; ?>>UTC</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="date_format" class="form-label"><?php _e('date_format'); ?></label>
                                <select class="form-select" id="date_format" name="date_format">
                                    <option value="Y-m-d" <?php echo ($currentSettings['date_format'] === 'Y-m-d') ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                    <option value="m/d/Y" <?php echo ($currentSettings['date_format'] === 'm/d/Y') ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                    <option value="d/m/Y" <?php echo ($currentSettings['date_format'] === 'd/m/Y') ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                    <option value="F j, Y" <?php echo ($currentSettings['date_format'] === 'F j, Y') ? 'selected' : ''; ?>>Month DD, YYYY</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="time_format" class="form-label"><?php _e('time_format'); ?></label>
                                <select class="form-select" id="time_format" name="time_format">
                                    <option value="H:i" <?php echo ($currentSettings['time_format'] === 'H:i') ? 'selected' : ''; ?>>24-hour (HH:MM)</option>
                                    <option value="g:i A" <?php echo ($currentSettings['time_format'] === 'g:i A') ? 'selected' : ''; ?>>12-hour (H:MM AM/PM)</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="currency_symbol" class="form-label"><?php _e('currency_symbol'); ?></label>
                                <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                       value="<?php echo htmlspecialchars($currentSettings['currency_symbol']); ?>" placeholder="$">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="currency_code" class="form-label"><?php _e('currency_code'); ?></label>
                                <input type="text" class="form-control" id="currency_code" name="currency_code"
                                       value="<?php echo htmlspecialchars($currentSettings['currency_code']); ?>" placeholder="USD">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> <?php _e('system_preferences'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="items_per_page" class="form-label"><?php _e('items_per_page'); ?></label>
                                <select class="form-select" id="items_per_page" name="items_per_page">
                                    <option value="10" <?php echo ($currentSettings['items_per_page'] === '10') ? 'selected' : ''; ?>>10</option>
                                    <option value="25" <?php echo ($currentSettings['items_per_page'] === '25') ? 'selected' : ''; ?>>25</option>
                                    <option value="50" <?php echo ($currentSettings['items_per_page'] === '50') ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo ($currentSettings['items_per_page'] === '100') ? 'selected' : ''; ?>>100</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="session_timeout" class="form-label"><?php _e('session_timeout'); ?> (<?php _e('seconds'); ?>)</label>
                                <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                       value="<?php echo htmlspecialchars($currentSettings['session_timeout']); ?>" min="300" max="86400">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="max_upload_size" class="form-label"><?php _e('max_upload_size'); ?> (MB)</label>
                                <input type="number" class="form-control" id="max_upload_size" name="max_upload_size"
                                       value="<?php echo htmlspecialchars($currentSettings['max_upload_size']); ?>" min="1" max="100">
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> <?php _e('save_system_settings'); ?>
                </button>
            </form>
        </div>

    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include "includes/footer.php"; ?>
