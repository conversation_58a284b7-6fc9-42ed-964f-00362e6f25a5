<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Gifts - Improved Design Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            color: #212529;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
        }

        .gifts-container {
            margin-top: 2rem;
        }

        .gift-card {
            border: none;
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-radius: 20px;
            overflow: visible;
            background: white;
            min-height: 450px;
            display: flex;
            flex-direction: column;
        }

        .gift-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        }

        .pending-gift {
            border-left: 6px solid #ffc107;
            background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
        }

        .sent-gift-card {
            border-left: 6px solid #007bff;
            background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
        }

        .gift-file-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .gift-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: -1.25rem -1.25rem 1.25rem -1.25rem;
            border-radius: 20px 20px 0 0;
        }

        .gift-card-body {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .gift-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
        }

        .gift-type-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .gift-message {
            font-size: 1rem;
            line-height: 1.6;
            color: #555;
            margin-bottom: 20px;
            flex: 1;
        }

        .gift-meta {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
        }

        .gift-actions {
            background: #f8f9fa;
            padding: 20px;
            margin: 0 -25px -25px -25px;
            border-radius: 0 0 20px 20px;
            margin-top: auto;
        }

        .action-btn {
            border-radius: 25px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-bottom: 2px solid transparent;
        }

        .nav-tabs .nav-link.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-house-heart"></i> Freedom Assembly Church
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#"><i class="bi bi-speedometer2"></i> Dashboard</a>
                <a class="nav-link" href="#"><i class="bi bi-calendar-event"></i> Events</a>
                <a class="nav-link active" href="#"><i class="bi bi-gift"></i> My Gifts</a>
            </div>
        </div>
    </nav>

    <div class="container gifts-container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-gift"></i> My Gifts</h2>
                    <div>
                        <button class="btn btn-outline-secondary me-2">
                            <i class="bi bi-download"></i> Download Summary
                        </button>
                        <a href="#" class="btn btn-primary">
                            <i class="bi bi-send"></i> Send a Gift
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="giftTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="received-tab" data-bs-toggle="tab" data-bs-target="#received" type="button" role="tab">
                    <i class="bi bi-inbox"></i> Received Gifts (3)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                    <i class="bi bi-send"></i> Sent Gifts (2)
                </button>
            </li>
        </ul>

        <div class="tab-content" id="giftTabContent">
            <!-- Received Gifts -->
            <div class="tab-pane fade show active" id="received" role="tabpanel">
                <div class="mt-4">
                    <div class="row">
                        <!-- Gift Card 1 -->
                        <div class="col-lg-6 col-xl-4 mb-5">
                            <div class="card gift-card">
                                <!-- Gift Card Header -->
                                <div class="gift-card-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="gift-title mb-0">Happy Birthday!</h5>
                                        <span class="gift-type-badge">Photo</span>
                                    </div>
                                </div>

                                <!-- Gift Card Body -->
                                <div class="gift-card-body">
                                    <div class="text-center mb-3">
                                        <img src="https://via.placeholder.com/300x200/667eea/ffffff?text=Birthday+Gift" 
                                             class="gift-file-preview" alt="Gift Image">
                                    </div>
                                    
                                    <div class="gift-message">You are loved and cherished! Wishing you a wonderful birthday filled with joy, blessings, and happiness!</div>
                                    
                                    <!-- Gift Meta Information -->
                                    <div class="gift-meta">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted d-block">From:</small>
                                                <strong>Godwin Bointa</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <small class="text-muted d-block">Received:</small>
                                                <strong>Jan 8, 2025</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gift Actions -->
                                <div class="gift-actions">
                                    <div class="row g-2">
                                        <!-- Primary Actions -->
                                        <div class="col-12 mb-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-primary action-btn">
                                                    <i class="bi bi-file-pdf"></i> PDF
                                                </button>
                                                <button class="btn btn-success action-btn">
                                                    <i class="bi bi-image"></i> JPEG
                                                </button>
                                                <button class="btn btn-info action-btn">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Secondary Actions -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-outline-secondary action-btn">
                                                    <i class="bi bi-share"></i> Share
                                                </button>
                                                <button class="btn btn-outline-danger action-btn">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gift Card 2 -->
                        <div class="col-lg-6 col-xl-4 mb-5">
                            <div class="card gift-card">
                                <!-- Gift Card Header -->
                                <div class="gift-card-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="gift-title mb-0">Happy Birthday!</h5>
                                        <span class="gift-type-badge">Digital_card</span>
                                    </div>
                                </div>

                                <!-- Gift Card Body -->
                                <div class="gift-card-body">
                                    <div class="text-center mb-3">
                                        <img src="https://via.placeholder.com/300x200/764ba2/ffffff?text=Digital+Card" 
                                             class="gift-file-preview" alt="Gift Image">
                                    </div>
                                    
                                    <div class="gift-message">Dear Churks Mike, Wishing you a wonderful birthday filled with joy, blessings, and happiness! With warm wishes, Godwin Bointa</div>
                                    
                                    <!-- Gift Meta Information -->
                                    <div class="gift-meta">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted d-block">From:</small>
                                                <strong>Godwin Bointa</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <small class="text-muted d-block">Received:</small>
                                                <strong>Jan 8, 2025</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gift Actions -->
                                <div class="gift-actions">
                                    <div class="row g-2">
                                        <!-- Primary Actions -->
                                        <div class="col-12 mb-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-primary action-btn">
                                                    <i class="bi bi-file-pdf"></i> PDF
                                                </button>
                                                <button class="btn btn-success action-btn">
                                                    <i class="bi bi-image"></i> JPEG
                                                </button>
                                                <button class="btn btn-info action-btn">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Secondary Actions -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-outline-secondary action-btn">
                                                    <i class="bi bi-share"></i> Share
                                                </button>
                                                <button class="btn btn-outline-danger action-btn">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gift Card 3 -->
                        <div class="col-lg-6 col-xl-4 mb-5">
                            <div class="card gift-card">
                                <!-- Gift Card Header -->
                                <div class="gift-card-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="gift-title mb-0">Happy Birthday!</h5>
                                        <span class="gift-type-badge">Digital_card</span>
                                    </div>
                                </div>

                                <!-- Gift Card Body -->
                                <div class="gift-card-body">
                                    <div class="text-center mb-3">
                                        <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Birthday+Wishes" 
                                             class="gift-file-preview" alt="Gift Image">
                                    </div>
                                    
                                    <div class="gift-message">Dear Churks Mike, Wishing you a wonderful birthday filled with joy, blessings, and happiness! With warm wishes, Godwin Bointa</div>
                                    
                                    <!-- Gift Meta Information -->
                                    <div class="gift-meta">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted d-block">From:</small>
                                                <strong>Godwin Bointa</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <small class="text-muted d-block">Received:</small>
                                                <strong>Jan 8, 2025</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gift Actions -->
                                <div class="gift-actions">
                                    <div class="row g-2">
                                        <!-- Primary Actions -->
                                        <div class="col-12 mb-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-primary action-btn">
                                                    <i class="bi bi-file-pdf"></i> PDF
                                                </button>
                                                <button class="btn btn-success action-btn">
                                                    <i class="bi bi-image"></i> JPEG
                                                </button>
                                                <button class="btn btn-info action-btn">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Secondary Actions -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-outline-secondary action-btn">
                                                    <i class="bi bi-share"></i> Share
                                                </button>
                                                <button class="btn btn-outline-danger action-btn">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sent Gifts Tab -->
            <div class="tab-pane fade" id="sent" role="tabpanel">
                <div class="mt-4">
                    <div class="row">
                        <!-- Sent Gift Card 1 -->
                        <div class="col-lg-6 col-xl-4 mb-5">
                            <div class="card gift-card sent-gift-card">
                                <!-- Gift Card Header -->
                                <div class="gift-card-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="gift-title mb-0">Happy Birthday!</h5>
                                        <span class="gift-type-badge">Digital_card</span>
                                    </div>
                                </div>

                                <!-- Gift Card Body -->
                                <div class="gift-card-body">
                                    <div class="text-center mb-3">
                                        <img src="https://via.placeholder.com/300x200/ffc107/000000?text=Sent+Gift" 
                                             class="gift-file-preview" alt="Gift Image">
                                    </div>
                                    
                                    <div class="gift-message">Dear Mike John, Wishing you a wonderful birthday filled with joy, blessings, and happiness! With warm wishes, Godwin Bointa</div>
                                    
                                    <!-- Gift Meta Information -->
                                    <div class="gift-meta">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted d-block">To:</small>
                                                <strong>Mike John</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <small class="text-muted d-block">Status:</small>
                                                <strong><i class="bi bi-check-circle text-success"></i> Delivered</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gift Actions -->
                                <div class="gift-actions">
                                    <div class="row g-2">
                                        <!-- Primary Actions -->
                                        <div class="col-12 mb-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-primary action-btn">
                                                    <i class="bi bi-file-pdf"></i> PDF
                                                </button>
                                                <button class="btn btn-success action-btn">
                                                    <i class="bi bi-image"></i> JPEG
                                                </button>
                                                <button class="btn btn-info action-btn">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Secondary Actions -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-outline-secondary action-btn">
                                                    <i class="bi bi-share"></i> Share
                                                </button>
                                                <button class="btn btn-outline-danger action-btn">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
