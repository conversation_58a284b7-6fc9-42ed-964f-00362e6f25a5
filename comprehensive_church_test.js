/**
 * Comprehensive Test Suite for Freedom Assembly Church Management System
 * Tests: Registration Flow, Email Functionality, Admin Panel, Database Operations, Broken Links
 */

const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost/campaign';
const CHURCH_URL = `${BASE_URL}/church`;
const ADMIN_URL = `${CHURCH_URL}/admin`;
const TEST_EMAIL = '<EMAIL>';

// Test data
const testMember = {
    fullName: 'John Test Member',
    email: TEST_EMAIL,
    phoneNumber: '+1234567890',
    birthDate: '1990-06-26', // Today's date for birthday testing
    occupation: 'Software Tester',
    address: '123 Test Street, Test City'
};

const adminCredentials = {
    username: 'admin', // You may need to adjust these
    password: 'admin123'
};

test.describe('Church Management System - Comprehensive Tests', () => {
    
    test.beforeEach(async ({ page }) => {
        // Set longer timeout for database operations
        test.setTimeout(60000);
    });

    test('1. Landing Page - Load and Navigation Test', async ({ page }) => {
        console.log('Testing landing page...');
        
        await page.goto(BASE_URL);
        await expect(page).toHaveTitle(/Freedom Assembly Church International/);
        
        // Check main navigation links
        const navLinks = [
            { text: 'Home', href: '#hero' },
            { text: 'Mission', href: '#features' },
            { text: 'About', href: '#about' },
            { text: 'Events', href: '#birthdays' },
            { text: 'Contact', href: '#contact' },
            { text: 'Join Us', href: 'church/register.php' }
        ];
        
        for (const link of navLinks) {
            const navLink = page.locator(`a:has-text("${link.text}")`);
            await expect(navLink).toBeVisible();
            console.log(`✓ Navigation link "${link.text}" is visible`);
        }
        
        // Test Join Us button functionality
        await page.click('a:has-text("Join Our Brotherhood")');
        await page.waitForURL(`${CHURCH_URL}/index.php`);
        console.log('✓ Join Us button redirects correctly');
    });

    test('2. Member Registration Form Validation Test', async ({ page }) => {
        console.log('Testing member registration form validation...');

        await page.goto(`${CHURCH_URL}/index.php`);

        // Check if registration form is present
        await expect(page.locator('form')).toBeVisible();
        console.log('✓ Registration form is visible');

        // Test form field presence
        const requiredFields = [
            'input[name="full_name"], input[placeholder*="full name"], input[placeholder*="Full Name"]',
            'input[name="email"], input[type="email"], input[placeholder*="email"]',
            'input[name="phone"], input[name="phone_number"], input[placeholder*="phone"]',
            'input[name="birth_date"], input[type="date"], input[placeholder*="birth"]'
        ];

        for (const fieldSelector of requiredFields) {
            const field = page.locator(fieldSelector).first();
            if (await field.isVisible()) {
                console.log(`✓ Required field found: ${fieldSelector}`);
            } else {
                console.log(`⚠ Required field not found: ${fieldSelector}`);
            }
        }

        // Test submit button presence
        const submitButton = page.locator('button[type="submit"], input[type="submit"], button:has-text("Join"), button:has-text("Register"), button:has-text("Submit")').first();
        if (await submitButton.isVisible()) {
            console.log('✓ Submit button found');
        } else {
            console.log('⚠ Submit button not found');
        }

        console.log('✓ Member registration form validation completed');
    });

    test('3. Database Connectivity Test', async ({ page }) => {
        console.log('Testing database connectivity...');
        
        // Test API endpoints that interact with database
        const apiEndpoints = [
            `${CHURCH_URL}/api/birthdays.php`,
            `${CHURCH_URL}/api/check_database.php`,
            `${CHURCH_URL}/api/check_members_table.php`
        ];
        
        for (const endpoint of apiEndpoints) {
            try {
                await page.goto(endpoint);
                await page.waitForTimeout(2000);
                
                // Check if page loaded without fatal errors
                const pageContent = await page.content();
                if (!pageContent.includes('Fatal error') && !pageContent.includes('Connection failed')) {
                    console.log(`✓ Database endpoint accessible: ${endpoint}`);
                } else {
                    console.log(`⚠ Database endpoint has errors: ${endpoint}`);
                }
            } catch (error) {
                console.log(`⚠ Database endpoint failed: ${endpoint} - ${error.message}`);
            }
        }
    });

    test('4. Admin Panel Access Test', async ({ page }) => {
        console.log('Testing admin panel access...');
        
        await page.goto(`${ADMIN_URL}/login.php`);
        
        // Check if login form exists
        const loginForm = page.locator('form');
        if (await loginForm.isVisible()) {
            console.log('✓ Admin login form is visible');
            
            // Try to login (credentials may need adjustment)
            const usernameField = page.locator('input[name="username"], input[name="email"]');
            const passwordField = page.locator('input[name="password"]');
            
            if (await usernameField.isVisible() && await passwordField.isVisible()) {
                await usernameField.fill(adminCredentials.username);
                await passwordField.fill(adminCredentials.password);
                
                await page.click('button[type="submit"], input[type="submit"]');
                await page.waitForTimeout(3000);
                
                // Check if login was successful
                if (page.url().includes('/admin/index.php') || page.url().includes('/admin/dashboard')) {
                    console.log('✓ Admin login successful');
                } else {
                    console.log('⚠ Admin login may have failed - checking for error messages');
                }
            }
        } else {
            console.log('⚠ Admin login form not found');
        }
    });

    test('5. Email Testing Functionality', async ({ page }) => {
        console.log('Testing email functionality...');
        
        // Try to access admin panel for email testing
        await page.goto(`${ADMIN_URL}/index.php`);
        
        // Look for email test functionality
        const emailTestSelectors = [
            'input[name="test_email"]',
            '#test_email',
            'form[method="POST"]:has(input[type="email"])'
        ];
        
        let emailTestFound = false;
        for (const selector of emailTestSelectors) {
            if (await page.locator(selector).isVisible()) {
                emailTestFound = true;
                console.log('✓ Email test form found');
                
                // Fill in test email
                await page.fill(selector, TEST_EMAIL);
                
                // Look for name field
                const nameField = page.locator('input[name="test_name"]');
                if (await nameField.isVisible()) {
                    await nameField.fill('Test User');
                }
                
                // Submit email test
                const submitButton = page.locator('button:has-text("Send Test Email"), input[value*="Send"]');
                if (await submitButton.isVisible()) {
                    await submitButton.click();
                    await page.waitForTimeout(5000);
                    
                    // Check for success/error messages
                    const successMsg = page.locator('.alert-success, .success');
                    const errorMsg = page.locator('.alert-danger, .error');
                    
                    if (await successMsg.isVisible()) {
                        console.log('✓ Email test appears successful');
                    } else if (await errorMsg.isVisible()) {
                        const errorText = await errorMsg.textContent();
                        console.log(`⚠ Email test error: ${errorText}`);
                    }
                }
                break;
            }
        }
        
        if (!emailTestFound) {
            console.log('⚠ Email test functionality not found on current page');
        }
    });

    test('6. Broken Links and Resources Check', async ({ page }) => {
        console.log('Checking for broken links and resources...');
        
        const pagesToCheck = [
            BASE_URL,
            `${CHURCH_URL}/index.php`,
            `${ADMIN_URL}/login.php`
        ];
        
        for (const pageUrl of pagesToCheck) {
            try {
                await page.goto(pageUrl);
                console.log(`Checking page: ${pageUrl}`);
                
                // Check for broken images
                const images = await page.locator('img').all();
                for (let i = 0; i < Math.min(images.length, 10); i++) { // Limit to first 10 images
                    const img = images[i];
                    const src = await img.getAttribute('src');
                    if (src && !src.startsWith('data:')) {
                        try {
                            const response = await page.request.get(src);
                            if (response.status() >= 400) {
                                console.log(`⚠ Broken image: ${src} (Status: ${response.status()})`);
                            }
                        } catch (error) {
                            console.log(`⚠ Image check failed: ${src}`);
                        }
                    }
                }
                
                // Check for broken CSS files
                const cssLinks = await page.locator('link[rel="stylesheet"]').all();
                for (let i = 0; i < Math.min(cssLinks.length, 5); i++) { // Limit to first 5 CSS files
                    const link = cssLinks[i];
                    const href = await link.getAttribute('href');
                    if (href && !href.startsWith('data:')) {
                        try {
                            const response = await page.request.get(href);
                            if (response.status() >= 400) {
                                console.log(`⚠ Broken CSS: ${href} (Status: ${response.status()})`);
                            }
                        } catch (error) {
                            console.log(`⚠ CSS check failed: ${href}`);
                        }
                    }
                }
                
                console.log(`✓ Resource check completed for ${pageUrl}`);
                
            } catch (error) {
                console.log(`⚠ Failed to check page: ${pageUrl} - ${error.message}`);
            }
        }
    });

    test('7. Birthday Email System Test', async ({ page }) => {
        console.log('Testing birthday email system...');
        
        // Try to access birthday management
        const birthdayPages = [
            `${ADMIN_URL}/birthday.php`,
            `${ADMIN_URL}/send_birthday_emails.php`,
            `${CHURCH_URL}/birthday_reminders.php`
        ];
        
        for (const birthdayPage of birthdayPages) {
            try {
                await page.goto(birthdayPage);
                await page.waitForTimeout(2000);
                
                const pageContent = await page.content();
                if (!pageContent.includes('Fatal error') && !pageContent.includes('404')) {
                    console.log(`✓ Birthday page accessible: ${birthdayPage}`);
                    
                    // Look for birthday-related functionality
                    const birthdayElements = [
                        'button:has-text("Send Birthday")',
                        'input[type="email"]',
                        '.birthday',
                        '#birthday'
                    ];
                    
                    for (const element of birthdayElements) {
                        if (await page.locator(element).isVisible()) {
                            console.log(`✓ Birthday functionality found: ${element}`);
                        }
                    }
                } else {
                    console.log(`⚠ Birthday page has errors: ${birthdayPage}`);
                }
            } catch (error) {
                console.log(`⚠ Birthday page failed: ${birthdayPage} - ${error.message}`);
            }
        }
    });

    test('8. System Health Check', async ({ page }) => {
        console.log('Performing system health check...');
        
        // Check PHP info if available
        try {
            await page.goto(`${CHURCH_URL}/phpinfo.php`);
            if (page.url().includes('phpinfo.php')) {
                console.log('✓ PHP info accessible');
            }
        } catch (error) {
            console.log('⚠ PHP info not accessible (this is normal for security)');
        }
        
        // Check configuration files accessibility (should be protected)
        const protectedFiles = [
            `${CHURCH_URL}/config.php`,
            `${CHURCH_URL}/composer.json`,
            `${CHURCH_URL}/.env`
        ];
        
        for (const file of protectedFiles) {
            try {
                await page.goto(file);
                const content = await page.content();
                if (content.includes('<?php') || content.includes('{')) {
                    console.log(`⚠ Security issue: ${file} is publicly accessible`);
                } else {
                    console.log(`✓ Protected file properly secured: ${file}`);
                }
            } catch (error) {
                console.log(`✓ Protected file not accessible: ${file}`);
            }
        }
        
        console.log('✓ System health check completed');
    });
});
