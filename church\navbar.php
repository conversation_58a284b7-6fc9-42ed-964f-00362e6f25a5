<?php
// Include config to access organization functions
if (!function_exists('get_organization_name')) {
    require_once 'config.php';
}

// Get logo settings
$headerLogo = get_site_setting('header_logo', '');
$mainLogo = get_site_setting('main_logo', '');
$logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

// Determine current page for active nav highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, var(--bs-primary, #007bff), var(--bs-secondary, #6c757d));">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="index.html">
            <?php if (!empty($logoToUse) && file_exists($logoToUse)): ?>
                <img src="<?php echo htmlspecialchars($logoToUse); ?>"
                     alt="<?php echo htmlspecialchars(get_organization_name()); ?> Logo"
                     height="40" class="me-2">
            <?php else: ?>
                <i class="bi bi-house-heart me-2"></i>
            <?php endif; ?>
            <?php echo htmlspecialchars(get_organization_name()); ?>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage == 'index.html' || $currentPage == 'index.php') ? 'active' : ''; ?>" href="index.html">
                        <i class="bi bi-house me-1"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage == 'events.php') ? 'active' : ''; ?>" href="events.php">
                        <i class="bi bi-calendar-event me-1"></i>Events
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage == 'enhanced_donate.php') ? 'active' : ''; ?>" href="enhanced_donate.php">
                        <i class="bi bi-heart me-1"></i>Donate
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="user/login.php">
                        <i class="bi bi-person me-1"></i>Member Login
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>